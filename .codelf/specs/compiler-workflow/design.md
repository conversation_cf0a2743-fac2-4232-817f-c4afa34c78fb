# 编译器工作流程设计文档

## 概述

FuckMP Vue3 微信小程序编译器采用模块化架构，通过 parser → transformer → generator → runtime 的四阶段流程，将 Vue3 单文件组件转换为微信小程序原生代码。编译器支持智能运行时注入、插件化转换和按需代码生成。

## 架构设计

### 核心组件架构

```mermaid
graph TB
    A[Vue3MiniprogramCompiler] --> B[ConfigManager]
    A --> C[PluginManager]
    A --> D[RuntimeInjector]
    A --> E[Parser模块]
    A --> F[Transformer模块]
    A --> G[Generator模块]
    
    E --> E1[SFCParser]
    E --> E2[ScriptParser]
    E --> E3[TemplateParser]
    E --> E4[StyleParser]
    
    F --> F1[ScriptTransformer]
    F --> F2[TemplateTransformer]
    F --> F3[StyleTransformer]
    F --> F4[EnhancedScriptTransformer]
    
    G --> G1[ComponentGenerator]
    G --> G2[PageGenerator]
    G --> G3[ConfigGenerator]
    
    D --> D1[特性分析器]
    D --> D2[运行时打包器]
    D --> D3[代码注入器]
```

### 数据流架构

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Compiler as Vue3MiniprogramCompiler
    participant Parser as Parser模块
    participant Transformer as Transformer模块
    participant Generator as Generator模块
    participant Runtime as RuntimeInjector
    
    Client->>Compiler: compile()
    Compiler->>Parser: 解析Vue文件
    Parser-->>Compiler: ParseResult
    Compiler->>Transformer: 转换语法
    Transformer-->>Compiler: TransformResult
    Compiler->>Generator: 生成代码
    Generator-->>Compiler: GenerateResult
    Compiler->>Runtime: 注入运行时
    Runtime-->>Compiler: 注入完成
    Compiler-->>Client: CompileResult
```

## 组件设计

### 1. 主编译器 (Vue3MiniprogramCompiler)

**职责**: 协调整个编译流程，管理各个子模块

**核心方法**:
- `compile()`: 编译整个项目
- `compileFile()`: 编译单个文件
- `watch()`: 监听模式编译
- `parseAndTransform()`: 解析和转换流程
- `generateCode()`: 代码生成流程

**数据管理**:
```typescript
interface CompilerState {
  sourceFiles: Map<string, string>        // 源文件内容
  compiledPages: Map<string, any>         // 编译后的页面
  compiledComponents: Map<string, any>    // 编译后的组件
  appConfig: any                          // 应用配置
}
```

### 2. 解析器模块 (Parser)

#### SFCParser
**职责**: 解析 Vue 单文件组件，提取各个部分

**核心功能**:
- 使用 `@vue/compiler-sfc` 解析 .vue 文件
- 提取 script、template、style 部分
- 验证 SFC 结构的合法性
- 处理 TypeScript 和 SCSS 预处理

#### ScriptParser
**职责**: 解析 script 部分，提取 Vue3 语法元素

**解析内容**:
- 导入声明 (imports)
- 变量声明 (variables)
- 函数声明 (functions)
- Vue3 宏调用 (macros)
- 响应式变量识别

#### TemplateParser
**职责**: 解析 template 部分，构建模板 AST

**解析内容**:
- Vue 指令 (v-if, v-for, v-model)
- 插值表达式 ({{ }})
- 事件绑定 (@click)
- 属性绑定 (:prop)
- 插槽 (slot)

#### StyleParser
**职责**: 解析 style 部分，处理样式预处理

**解析内容**:
- SCSS/Sass 编译
- 作用域样式处理
- CSS 变量和混合宏
- 样式导入处理

### 3. 转换器模块 (Transformer)

#### ScriptTransformer
**职责**: 转换 script 部分的 Vue3 语法

**转换规则**:
```typescript
// Vue3 宏转换
defineProps<Props>() → properties: { ... }
defineEmits<Emits>() → methods: { ... }
ref(0) → data: { count: 0 }
reactive({}) → data: { ... }
computed(() => {}) → computed: { ... }

// 生命周期转换
onMounted() → onLoad() / attached()
onUnmounted() → onUnload() / detached()
```

#### TemplateTransformer
**职责**: 转换 template 部分的 Vue 语法

**转换规则**:
```xml
<!-- Vue 模板 -->
<div v-if="show">{{ message }}</div>
<div v-for="item in list" :key="item.id">{{ item.name }}</div>
<button @click="handleClick">点击</button>

<!-- 小程序模板 -->
<view wx:if="{{show}}">{{message}}</view>
<view wx:for="{{list}}" wx:key="id">{{item.name}}</view>
<button bind:tap="handleClick">点击</button>
```

#### StyleTransformer
**职责**: 转换 style 部分的样式语法

**转换规则**:
```scss
// Vue 样式
.component {
  width: 100px;
  height: 50px;
  @media (max-width: 768px) { ... }
}

// 小程序样式
.component {
  width: 200rpx;
  height: 100rpx;
  /* @media 查询被移除 */
}
```

#### EnhancedScriptTransformer
**职责**: 使用插件系统进行高级脚本转换

**插件化处理**:
- 响应式数据转换插件
- 双向绑定转换插件
- 条件渲染转换插件
- 列表渲染转换插件
- 插槽系统转换插件

### 4. 生成器模块 (Generator)

#### ComponentGenerator
**职责**: 生成小程序组件代码

**生成文件**:
```javascript
// .js 文件
Component({
  properties: { ... },
  data: { ... },
  methods: { ... },
  lifetimes: { ... }
})

// .json 文件
{
  "component": true,
  "usingComponents": { ... }
}

// .wxml 文件
<view class="component">...</view>

// .wxss 文件
.component { ... }
```

#### PageGenerator
**职责**: 生成小程序页面代码

**生成文件**:
```javascript
// .js 文件
Page({
  data: { ... },
  onLoad: function() { ... },
  methods: { ... }
})

// .json 文件
{
  "navigationBarTitleText": "页面标题",
  "usingComponents": { ... }
}
```

#### ConfigGenerator
**职责**: 生成项目配置文件

**生成文件**:
- `app.json`: 应用配置
- `project.config.json`: 项目配置
- `sitemap.json`: 站点地图
- `tsconfig.json`: TypeScript 配置

### 5. 运行时注入器 (RuntimeInjector)

#### 特性分析器
**职责**: 分析项目中使用的 Vue3 特性

**分析内容**:
```typescript
interface Vue3FeatureUsage {
  reactivity: {
    ref: boolean
    reactive: boolean
    computed: boolean
    watch: boolean
  }
  composition: {
    setup: boolean
    defineProps: boolean
    defineEmits: boolean
  }
  directives: {
    vIf: boolean
    vFor: boolean
    vModel: boolean
  }
  lifecycle: {
    onMounted: boolean
    onUnmounted: boolean
  }
}
```

#### 运行时打包器
**职责**: 根据特性使用情况打包运行时模块

**模块选择**:
```typescript
const runtimeModules = {
  core: '核心运行时',
  reactivity: '响应式系统',
  lifecycle: '生命周期管理',
  template: '模板引擎',
  event: '事件系统',
  component: '组件管理'
}
```

#### 代码注入器
**职责**: 将运行时代码注入到生成的小程序代码中

**注入策略**:
- 按需注入: 只包含使用的功能模块
- 代码压缩: 生产模式启用压缩
- 模块包装: 使用 IIFE 包装运行时代码

### 6. 插件系统

#### PluginManager
**职责**: 管理插件的注册、依赖解析和执行调度

**核心功能**:
- 插件注册和验证
- 依赖关系解析
- 优先级排序
- 执行调度和错误处理

#### TransformPlugin
**职责**: 插件基类，定义插件接口

**插件接口**:
```typescript
interface TransformPlugin {
  name: string
  version: string
  priority: number
  dependencies: string[]
  
  supports(node: ASTNode, context: TransformContext): boolean
  transform(node: ASTNode, context: TransformContext): Promise<TransformResult>
  initialize?(context: TransformContext): Promise<void>
  cleanup?(): Promise<void>
}
```

## 错误处理

### 错误分类
1. **解析错误**: Vue 文件语法错误
2. **转换错误**: 语法转换失败
3. **生成错误**: 代码生成失败
4. **运行时错误**: 运行时注入失败
5. **插件错误**: 插件执行异常

### 错误恢复策略
- **优雅降级**: 部分功能失败时继续编译
- **详细日志**: 提供错误位置和修复建议
- **回滚机制**: 插件执行失败时回滚状态
- **用户友好**: 显示清晰的错误信息

## 测试策略

### 单元测试
- 每个模块独立测试
- Mock 依赖模块
- 覆盖核心功能和边界情况

### 集成测试
- 端到端编译流程测试
- 多文件项目编译测试
- 插件系统集成测试

### 性能测试
- 编译速度基准测试
- 内存使用监控
- 大型项目压力测试

### 兼容性测试
- Vue3 特性兼容性测试
- 小程序平台兼容性测试
- TypeScript 版本兼容性测试